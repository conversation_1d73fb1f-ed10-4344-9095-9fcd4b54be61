﻿using Compunet.YoloSharp;
using MvCameraControl;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GGEC.AOI.T68ZJ.Managers
{
    public class InferenceManager
    {
        //private YoloPredictor? _predictor;
        //private YoloPredictor? _pinPredictor;
        //void Initialize()
        //{
        //    _predictor = new YoloPredictor(@".\Resources\Models\t68zj_0723_1145_best.onnx");
        //    _pinPredictor = new YoloPredictor(@".\Resources\Models\yolo_pin_obb_best.onnx");



        //}

        //void InferenceImage(IImage image)
        //{
        //    if (_predictor == null)
        //    {
        //        return null;
        //    }
        //    _predictor.Detect(image.PixelData);
        //}
    }
}

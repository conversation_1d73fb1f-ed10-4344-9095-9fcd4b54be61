using System;
using GGEC.AOI.T68ZJ.Log;
using MvCameraControl;

namespace GGEC.AOI.T68ZJ.Managers
{
    /// <summary>
    /// 相机管理器工厂类
    /// 用于在 CameraManager 和 CameraManager2 之间切换
    /// </summary>
    public static class CameraManagerFactory
    {
        /// <summary>
        /// 相机管理器类型枚举
        /// </summary>
        public enum CameraManagerType
        {
            /// <summary>
            /// 原始的 CameraManager（复杂缓存机制）
            /// </summary>
            Original,
            
            /// <summary>
            /// 新的 CameraManager2（参考 BasicDemoLineScan）
            /// </summary>
            Enhanced
        }

        private static CameraManagerType _currentType = CameraManagerType.Original;
        private static ICameraManager? _currentInstance;
        private static readonly object _lock = new object();

        /// <summary>
        /// 当前使用的相机管理器类型
        /// </summary>
        public static CameraManagerType CurrentType
        {
            get => _currentType;
            set
            {
                if (_currentType != value)
                {
                    lock (_lock)
                    {
                        if (_currentType != value)
                        {
                            Logger.Info($"切换相机管理器类型：{_currentType} -> {value}");
                            
                            // 清理当前实例
                            _currentInstance?.Dispose();
                            _currentInstance = null;
                            
                            _currentType = value;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 获取相机管理器实例
        /// </summary>
        public static ICameraManager GetInstance()
        {
            if (_currentInstance == null)
            {
                lock (_lock)
                {
                    if (_currentInstance == null)
                    {
                        _currentInstance = CreateInstance(_currentType);
                        Logger.Info($"创建相机管理器实例：{_currentType}");
                    }
                }
            }
            return _currentInstance;
        }

        /// <summary>
        /// 创建指定类型的相机管理器实例
        /// </summary>
        private static ICameraManager CreateInstance(CameraManagerType type)
        {
            return type switch
            {
                CameraManagerType.Original => new CameraManagerWrapper(CameraManager.Instance),
                CameraManagerType.Enhanced => new CameraManager2Wrapper(CameraManager2.Instance),
                _ => throw new ArgumentException($"不支持的相机管理器类型：{type}")
            };
        }

        /// <summary>
        /// 设置相机管理器类型（使用变量控制，不依赖配置文件）
        /// </summary>
        /// <param name="type">要设置的相机管理器类型</param>
        public static void SetCameraManagerType(CameraManagerType type)
        {
            try
            {
                CurrentType = type;
                Logger.Info($"设置相机管理器类型：{type}");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, $"设置相机管理器类型失败：{type}");
            }
        }



        /// <summary>
        /// 初始化相机管理器并加载配置
        /// </summary>
        public static void Initialize(CameraManagerType type)
        {
            try
            {
                CurrentType = type;

                // 获取实例并加载配置
                var instance = GetInstance();
                if (instance is CameraManagerWrapper wrapper)
                {
                    wrapper.GetCameraManager().LoadConfigFromAppConfig();
                }
                else if (instance is CameraManager2Wrapper wrapper2)
                {
                    wrapper2.GetCameraManager2().LoadConfigFromAppConfig();
                }

                Logger.Info($"初始化相机管理器类型并加载配置：{type}");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, $"初始化相机管理器类型失败，使用默认类型：{type}");
            }
        }

        /// <summary>
        /// 释放当前实例
        /// </summary>
        public static void Dispose()
        {
            lock (_lock)
            {
                _currentInstance?.Dispose();
                _currentInstance = null;
                Logger.Info("相机管理器工厂已释放");
            }
        }
    }

    /// <summary>
    /// 相机管理器接口
    /// 定义统一的相机管理器接口
    /// </summary>
    public interface ICameraManager : IDisposable
    {
        // 基本属性
        double ExposureTime { get; set; }
        double Gain { get; set; }
        string PixelFormat { get; set; }
        bool AutoExposureEnabled { get; set; }
        bool AutoGainEnabled { get; set; }
        bool ShowLivePreview { get; set; }
        int DeviceCount { get; }
        bool IsGrabbing { get; }

        // 兼容性属性
        double ConveyorBeltSpeed { get; set; }
        int SensorTriggerDelayCompensation { get; set; }
        int MinPhotoIntervalMs { get; set; }

        // 核心方法
        void InitCameras();
        void OpenDevices(int deviceCount = 2);
        void CloseDevices();
        void StartLivePreview(params System.Windows.Forms.PictureBox[] pictureBoxes);
        void StopLivePreview();
        IImage? TakePhoto(int deviceIndex);
        (IImage? image1, IImage? image2) TakeSensorTriggeredPhotos();
        System.Threading.Tasks.Task<(IImage? image1, IImage? image2)> TakeSensorTriggeredPhotosAsync();
        
        // 图像保存方法
        int SaveBmpImage(int deviceIndex, string? customPath = null);
        int SaveJpegImage(int deviceIndex, int quality = 80, string? customPath = null);
        int SavePngImage(int deviceIndex, string? customPath = null);

        // 工具方法
        string GetMemoryUsage();
        void AutoAdjustForConveyorSpeed();
    }

    /// <summary>
    /// CameraManager 包装器
    /// </summary>
    public class CameraManagerWrapper : ICameraManager
    {
        private readonly CameraManager _manager;

        public CameraManagerWrapper(CameraManager manager)
        {
            _manager = manager ?? throw new ArgumentNullException(nameof(manager));
        }

        /// <summary>
        /// 获取底层的CameraManager实例
        /// </summary>
        public CameraManager GetCameraManager() => _manager;

        public double ExposureTime { get => _manager.ExposureTime; set => _manager.ExposureTime = value; }
        public double Gain { get => _manager.Gain; set => _manager.Gain = value; }
        public string PixelFormat { get => _manager.PixelFormat; set => _manager.PixelFormat = value; }
        public bool AutoExposureEnabled { get => _manager.AutoExposureEnabled; set => _manager.AutoExposureEnabled = value; }
        public bool AutoGainEnabled { get => _manager.AutoGainEnabled; set => _manager.AutoGainEnabled = value; }
        public bool ShowLivePreview { get => _manager.ShowLivePreview; set => _manager.ShowLivePreview = value; }
        public double ConveyorBeltSpeed { get => _manager.ConveyorBeltSpeed; set => _manager.ConveyorBeltSpeed = value; }
        public int SensorTriggerDelayCompensation { get => _manager.SensorTriggerDelayCompensation; set => _manager.SensorTriggerDelayCompensation = value; }
        public int MinPhotoIntervalMs { get => _manager.MinPhotoIntervalMs; set => _manager.MinPhotoIntervalMs = value; }

        public int DeviceCount => 2; // CameraManager 固定支持2个设备
        public bool IsGrabbing => true; // CameraManager 没有直接的状态属性

        public void InitCameras() => _manager.InitCameras();
        public void OpenDevices(int deviceCount = 2) => _manager.InitCameras(); // CameraManager 在 InitCameras 中打开设备
        public void CloseDevices() { /* CameraManager 没有单独的关闭方法 */ }
        public void StartLivePreview(params System.Windows.Forms.PictureBox[] pictureBoxes)
        {
            if (pictureBoxes.Length >= 2)
                _manager.StartLivePreview(pictureBoxes[0], pictureBoxes[1]);
        }
        public void StopLivePreview() => _manager.StopLivePreview();
        public IImage? TakePhoto(int deviceIndex) => _manager.TakePhoto(deviceIndex);
        public (IImage? image1, IImage? image2) TakeSensorTriggeredPhotos() => _manager.TakeSensorTriggeredPhotos();
        public System.Threading.Tasks.Task<(IImage? image1, IImage? image2)> TakeSensorTriggeredPhotosAsync() => _manager.TakeSensorTriggeredPhotosAsync();
        public string GetMemoryUsage() => _manager.GetMemoryUsage();
        public void AutoAdjustForConveyorSpeed() => _manager.AutoAdjustForConveyorSpeed();

        // 图像保存方法（CameraManager 没有直接的保存方法，返回成功状态）
        public int SaveBmpImage(int deviceIndex, string? customPath = null) => MvError.MV_OK;
        public int SaveJpegImage(int deviceIndex, int quality = 80, string? customPath = null) => MvError.MV_OK;
        public int SavePngImage(int deviceIndex, string? customPath = null) => MvError.MV_OK;

        public void Dispose()
        {
            // CameraManager 是单例，不需要释放
        }
    }

    /// <summary>
    /// CameraManager2 包装器
    /// </summary>
    public class CameraManager2Wrapper : ICameraManager
    {
        private readonly CameraManager2 _manager;

        public CameraManager2Wrapper(CameraManager2 manager)
        {
            _manager = manager ?? throw new ArgumentNullException(nameof(manager));
        }

        /// <summary>
        /// 获取底层的CameraManager2实例
        /// </summary>
        public CameraManager2 GetCameraManager2() => _manager;

        public double ExposureTime { get => _manager.ExposureTime; set => _manager.ExposureTime = value; }
        public double Gain { get => _manager.Gain; set => _manager.Gain = value; }
        public string PixelFormat { get => _manager.PixelFormat; set => _manager.PixelFormat = value; }
        public bool AutoExposureEnabled { get => _manager.AutoExposureEnabled; set => _manager.AutoExposureEnabled = value; }
        public bool AutoGainEnabled { get => _manager.AutoGainEnabled; set => _manager.AutoGainEnabled = value; }
        public bool ShowLivePreview { get => _manager.ShowLivePreview; set => _manager.ShowLivePreview = value; }
        public double ConveyorBeltSpeed { get => _manager.ConveyorBeltSpeed; set => _manager.ConveyorBeltSpeed = value; }
        public int SensorTriggerDelayCompensation { get => _manager.SensorTriggerDelayCompensation; set => _manager.SensorTriggerDelayCompensation = value; }
        public int MinPhotoIntervalMs { get => _manager.MinPhotoIntervalMs; set => _manager.MinPhotoIntervalMs = value; }
        public int DeviceCount => _manager.DeviceCount;
        public bool IsGrabbing => _manager.IsGrabbing;

        public void InitCameras() => _manager.InitCameras();
        public void OpenDevices(int deviceCount = 2) => _manager.OpenDevices(deviceCount);
        public void CloseDevices() => _manager.CloseDevices();
        public void StartLivePreview(params System.Windows.Forms.PictureBox[] pictureBoxes) => _manager.StartLivePreview(pictureBoxes);
        public void StopLivePreview() => _manager.StopLivePreview();
        public IImage? TakePhoto(int deviceIndex) => _manager.TakePhoto(deviceIndex);
        public (IImage? image1, IImage? image2) TakeSensorTriggeredPhotos() => _manager.TakeSensorTriggeredPhotos();
        public System.Threading.Tasks.Task<(IImage? image1, IImage? image2)> TakeSensorTriggeredPhotosAsync() => _manager.TakeSensorTriggeredPhotosAsync();
        public int SaveBmpImage(int deviceIndex, string? customPath = null) => _manager.SaveBmpImage(deviceIndex, customPath);
        public int SaveJpegImage(int deviceIndex, int quality = 80, string? customPath = null) => _manager.SaveJpegImage(deviceIndex, quality, customPath);
        public int SavePngImage(int deviceIndex, string? customPath = null) => _manager.SavePngImage(deviceIndex, customPath);
        public string GetMemoryUsage() => _manager.GetMemoryUsage();
        public void AutoAdjustForConveyorSpeed() => _manager.AutoAdjustForConveyorSpeed();

        public void Dispose()
        {
            _manager?.Dispose();
        }
    }
}

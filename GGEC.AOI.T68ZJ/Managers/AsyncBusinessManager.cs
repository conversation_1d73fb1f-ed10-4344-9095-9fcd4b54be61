using GGEC.AOI.T68ZJ.Log;
using System.Diagnostics;

namespace GGEC.AOI.T68ZJ.Managers
{
    /// <summary>
    /// 简化的异步业务管理器 - 专注于基本异步检测
    /// </summary>
    public class AsyncBusinessManager : IDisposable
    {
        private readonly ImageProcessingManager _processingManager;
        private bool _isDisposed = false;

        public AsyncBusinessManager(ImageProcessingManager processingManager)
        {
            _processingManager = processingManager ?? throw new ArgumentNullException(nameof(processingManager));
            Logger.Info("简化异步业务管理器已初始化");
        }

        /// <summary>
        /// 简化的异步检测流程 - 专注耗时统计
        /// </summary>
        public async Task<DetectionSummary> ExecuteDetectionWorkflowAsync(int camera1Index = 0, int camera2Index = 1, string outputPrefix = null)
        {
            if (_isDisposed)
                throw new ObjectDisposedException(nameof(AsyncBusinessManager));

            var totalStopwatch = Stopwatch.StartNew();
            Logger.Info("=== 开始简化异步检测工作流 ===");

            try
            {
                // 步骤1: 并行拍照
                var cameraStopwatch = Stopwatch.StartNew();
                var imageTask1 = Task.Run(() => CameraManager.Instance.TakePhoto(camera1Index));
                var imageTask2 = Task.Run(() => CameraManager.Instance.TakePhoto(camera2Index));

                var images = await Task.WhenAll(imageTask1, imageTask2);
                var image1 = images[0];
                var image2 = images[1];
                cameraStopwatch.Stop();
                Logger.Info($"【耗时统计】并行拍照耗时：{cameraStopwatch.ElapsedMilliseconds}ms");

                if (image1 == null || image2 == null)
                {
                    throw new InvalidOperationException($"拍照失败 - 相机1：{(image1 != null ? "成功" : "失败")}，相机2：{(image2 != null ? "成功" : "失败")}");
                }

                // 步骤2: 异步图像处理和推理
                var processingStopwatch = Stopwatch.StartNew();
                var summary = await _processingManager.ProcessDualCameraImagesAsync(image1, image2, outputPrefix);
                processingStopwatch.Stop();
                Logger.Info($"【耗时统计】异步图像处理耗时：{processingStopwatch.ElapsedMilliseconds}ms");

                totalStopwatch.Stop();
                Logger.Info($"【耗时统计】总检测流程耗时：{totalStopwatch.ElapsedMilliseconds}ms");
                Logger.Info("=== 简化异步检测工作流完成 ===");

                return summary;
            }
            catch (Exception ex)
            {
                totalStopwatch.Stop();
                Logger.Exception(ex, $"异步检测工作流异常，总耗时：{totalStopwatch.ElapsedMilliseconds}ms");
                throw;
            }
        }



        public void Dispose()
        {
            if (!_isDisposed)
            {
                _isDisposed = true;
                Logger.Info("简化异步业务管理器已释放");
            }
        }
    }
}

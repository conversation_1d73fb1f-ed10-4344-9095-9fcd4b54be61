using GGEC.AOI.T68ZJ.Log;
using MvCameraControl;
using System.Diagnostics;
using System.Linq;

namespace GGEC.AOI.T68ZJ.Managers
{
    /// <summary>
    /// 点位检测结果
    /// </summary>
    public class PointDetectionResult
    {
        public bool IsOk { get; set; }
        public string Message { get; set; } = "";
        public double ProcessingTime { get; set; }
    }

    /// <summary>
    /// 检测结果汇总
    /// </summary>
    public class DetectionSummary
    {
        public InferenceResult Camera1Result { get; set; }
        public InferenceResult Camera2Result { get; set; }
        public bool OverallResult { get; set; }
        public string ResultMessage { get; set; }
        public double TotalProcessingTime { get; set; }

        // NG原因汇总
        public List<string> NgReasons { get; set; } = new List<string>();

        // 三个点位的检测结果
        public PointDetectionResult Point1Result { get; set; } = new PointDetectionResult();
        public PointDetectionResult Point2Result { get; set; } = new PointDetectionResult();
        public PointDetectionResult Point3Result { get; set; } = new PointDetectionResult();

        // 针脚倾斜检测结果
        public PointDetectionResult PinTiltResult { get; set; } = new PointDetectionResult();

        // 详细日志信息
        public List<string> DetailedLogs { get; set; } = new List<string>();
    }

    /// <summary>
    /// 简化的检测规则
    /// </summary>
    public class DetectionRules
    {
        public float MinConfidence { get; set; } = 0.5f;

        public bool IsOkResult(List<KeyValuePair<string, float>> detections)
        {
            return detections.Any(d => d.Key.Contains("ok") && d.Value >= MinConfidence);
        }

        public bool IsNgResult(List<KeyValuePair<string, float>> detections)
        {
            return detections.Any(d => d.Key.Contains("ng") && d.Value >= MinConfidence);
        }
    }

    /// <summary>
    /// 简化的图像处理管理器
    /// </summary>
    public class ImageProcessingManager : IDisposable
    {
        private readonly YoloInferenceManager _inferenceHelper;
        private readonly DetectionRules _detectionRules;
        private bool _isDisposed = false;

        public ImageProcessingManager(InferenceConfig config = null, DetectionRules rules = null)
        {
            _inferenceHelper = new YoloInferenceManager(config);
            _detectionRules = rules ?? new DetectionRules();

            // 默认启用性能优化模式
            //_inferenceHelper.EnablePerformanceMode();
            Logger.Info("图像处理管理器已初始化");
        }

        /// <summary>
        /// 处理单个相机的图像
        /// </summary>
        public InferenceResult ProcessSingleCamera(IFrameOut frameOut, int cameraId, string outputPrefix = null)
        {
            if (_isDisposed)
                throw new ObjectDisposedException(nameof(ImageProcessingManager));

            if (frameOut?.Image == null)
                throw new ArgumentNullException(nameof(frameOut), "输入帧为空");

            return ProcessSingleCameraImage(frameOut.Image, cameraId, outputPrefix);
        }

        /// <summary>
        /// 处理单个相机的图像 - 异步版本
        /// </summary>
        public async Task<InferenceResult> ProcessSingleCameraImageAsync(IImage image, int cameraId, string outputPrefix = null)
        {
            if (_isDisposed)
                throw new ObjectDisposedException(nameof(ImageProcessingManager));

            if (image == null)
                throw new ArgumentNullException(nameof(image), "输入图像为空");

            var prefix = outputPrefix ?? $"camera_{cameraId}_{DateTime.Now:yyyyMMddHHmmss}";
            return await _inferenceHelper.InferFromImageAsync(image, prefix);
        }

        /// <summary>
        /// 处理单个相机的图像 - 同步版本（保持向后兼容）
        /// </summary>
        public InferenceResult ProcessSingleCameraImage(IImage image, int cameraId, string outputPrefix = null)
        {
            return ProcessSingleCameraImageAsync(image, cameraId, outputPrefix).GetAwaiter().GetResult();
        }

        /// <summary>
        /// 处理双相机图像
        /// </summary>
        public DetectionSummary ProcessDualCamera(IFrameOut frame1, IFrameOut frame2, string outputPrefix = null)
        {
            if (_isDisposed)
                throw new ObjectDisposedException(nameof(ImageProcessingManager));

            if (frame1?.Image == null || frame2?.Image == null)
                throw new ArgumentNullException("输入帧为空");

            return ProcessDualCameraImages(frame1.Image, frame2.Image, outputPrefix);
        }

        /// <summary>
        /// 处理双相机图像 - 异步版本
        /// </summary>
        public async Task<DetectionSummary> ProcessDualCameraImagesAsync(IImage image1, IImage image2, string outputPrefix = null)
        {
            if (_isDisposed)
                throw new ObjectDisposedException(nameof(ImageProcessingManager));

            if (image1 == null || image2 == null)
                throw new ArgumentNullException("输入图像为空");

            var sw = Stopwatch.StartNew();
            var summary = new DetectionSummary();
            var prefix = outputPrefix ?? DateTime.Now.ToString("yyyyMMddHHmmss");

            try
            {
                // 并行处理两个相机的图像 - 使用异步方法
                var tasks = new[]
                {
                    ProcessSingleCameraImageAsync(image1, 0, $"{prefix}_0"),
                    ProcessSingleCameraImageAsync(image2, 1, $"{prefix}_1")
                };

                var results = await Task.WhenAll(tasks);

                summary.Camera1Result = results[0];
                summary.Camera2Result = results[1];

                // 在子线程中进行三个点位的检测评估
                var evaluationStopwatch = Stopwatch.StartNew();
                await Task.Run(() => EvaluateDetailedResults(summary.Camera1Result.Detections, summary.Camera2Result.Detections, summary, prefix));
                evaluationStopwatch.Stop();

                sw.Stop();
                summary.TotalProcessingTime = sw.Elapsed.TotalMilliseconds;

                // 整体结果：三个点位和针脚倾斜都OK才算PASS
                summary.OverallResult = summary.Point1Result.IsOk && summary.Point2Result.IsOk && summary.Point3Result.IsOk && summary.PinTiltResult.IsOk;

                // 设置结果消息，包含简单的NG原因
                if (summary.OverallResult)
                {
                    summary.ResultMessage = "OK";
                }
                else
                {
                    if (summary.NgReasons.Count > 0)
                    {
                        // 只显示前3个原因，避免消息过长
                        var displayReasons = summary.NgReasons.Take(3).ToList();
                        summary.ResultMessage = $"NG ({string.Join("; ", displayReasons)})";
                        if (summary.NgReasons.Count > 3)
                        {
                            summary.ResultMessage += $" 等{summary.NgReasons.Count}项";
                        }
                    }
                    else
                    {
                        summary.ResultMessage = "NG";
                    }
                }

                return summary;
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "双相机处理异常");
                throw;
            }
        }

        /// <summary>
        /// 处理双相机图像 - 同步版本（保持向后兼容）
        /// </summary>
        public DetectionSummary ProcessDualCameraImages(IImage image1, IImage image2, string outputPrefix = null)
        {
            return ProcessDualCameraImagesAsync(image1, image2, outputPrefix).GetAwaiter().GetResult();
        }

        /// <summary>
        /// 按照原始逻辑进行详细的三点位检测评估
        /// </summary>
        private void EvaluateDetailedResults(List<KeyValuePair<string, float>> re1,
                                           List<KeyValuePair<string, float>> re2,
                                           DetectionSummary summary,
                                           string prefix)
        {
            var datetimestr = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            // 【点位1】校验
            // 【点位1】校验
            var dw1Watch = Stopwatch.StartNew();
            Log("【点位1】校验开始");
            summary.DetailedLogs.Add("【点位1】校验开始");

            if ((re1.Any(x => x.Key.Contains("cok1")) && re2.Any(x => x.Key.Contains("fok1"))) ||
                (re1.Any(x => x.Key.Contains("cok1")) && !re2.Any(x => x.Key.Contains("fng1"))) ||
                (!re1.Any(x => x.Key.Contains("cng1")) && re2.Any(x => x.Key.Contains("fok1"))))
            {
                summary.Point1Result.IsOk = true;
            }
            else
            {
                if (!re2.Any(x => x.Key.Contains("fng1")) && !re1.Any(x => x.Key.Contains("cng1")))
                {
                    summary.Point1Result.Message = "缺少检验目标";
                    summary.NgReasons.Add("点位1：缺少检验目标");
                }
                else
                {
                    summary.Point1Result.Message = "检验不通过";
                    summary.NgReasons.Add("点位1：检验不通过");
                }
            }
            dw1Watch.Stop();
            summary.Point1Result.ProcessingTime = dw1Watch.ElapsedMilliseconds;

            var dw1LogMsg = $"【点位1】校验结束，结果：{(summary.Point1Result.IsOk ? "OK" : "NG")}，耗时：{dw1Watch.ElapsedMilliseconds}ms";
            Log(dw1LogMsg);
            summary.DetailedLogs.Add(dw1LogMsg);
            summary.DetailedLogs.Add($"点位1：校验【{(summary.Point1Result.IsOk ? "OK" : "NG")}】，时间【{datetimestr}】");

            // 【点位2】校验
            var dw2Watch = Stopwatch.StartNew();
            Log("【点位2】校验开始");
            summary.DetailedLogs.Add("【点位2】校验开始");

            if ((re1.Any(x => x.Key.Contains("cok2")) && re2.Any(x => x.Key.Contains("fok2"))) ||
                (re1.Any(x => x.Key.Contains("cok2")) && !re2.Any(x => x.Key.Contains("fng2"))) ||
                (!re1.Any(x => x.Key.Contains("cng2")) && re2.Any(x => x.Key.Contains("fok2"))))
            {
                summary.Point2Result.IsOk = true;
            }
            else
            {
                if (!re2.Any(x => x.Key.Contains("fng2")) && !re1.Any(x => x.Key.Contains("cng2")))
                {
                    summary.Point2Result.Message = "缺少检验目标";
                    summary.NgReasons.Add("点位2：缺少检验目标");
                }
                else
                {
                    summary.Point2Result.Message = "检验不通过";
                    summary.NgReasons.Add("点位2：检验不通过");
                }
            }
            dw2Watch.Stop();
            summary.Point2Result.ProcessingTime = dw2Watch.ElapsedMilliseconds;

            var dw2LogMsg = $"【点位2】校验结束，结果：{(summary.Point2Result.IsOk ? "OK" : "NG")}，耗时：{dw2Watch.ElapsedMilliseconds}ms";
            Log(dw2LogMsg);
            summary.DetailedLogs.Add(dw2LogMsg);
            summary.DetailedLogs.Add($"点位2：校验【{(summary.Point2Result.IsOk ? "OK" : "NG")}】，时间【{datetimestr}】");

            // 【点位3】校验
            var dw3Watch = Stopwatch.StartNew();
            Log("【点位3】校验开始");
            summary.DetailedLogs.Add("【点位3】校验开始");

            if ((re1.Any(x => x.Key.Contains("cok3")) && re2.Any(x => x.Key.Contains("fok3"))) ||
                (re1.Any(x => x.Key.Contains("cok3")) && !re2.Any(x => x.Key.Contains("fng3"))) ||
                (!re1.Any(x => x.Key.Contains("cng3")) && re2.Any(x => x.Key.Contains("fok3"))))
            {
                summary.Point3Result.IsOk = true;
            }
            else
            {
                if (!re2.Any(x => x.Key.Contains("fng3")) && !re1.Any(x => x.Key.Contains("cng3")))
                {
                    summary.Point3Result.Message = "缺少检验目标";
                    summary.NgReasons.Add("点位3：缺少检验目标");
                }
                else
                {
                    summary.Point3Result.Message = "检验不通过";
                    summary.NgReasons.Add("点位3：检验不通过");
                }
            }
            dw3Watch.Stop();
            summary.Point3Result.ProcessingTime = dw3Watch.ElapsedMilliseconds;

            var dw3LogMsg = $"【点位3】校验结束，结果：{(summary.Point3Result.IsOk ? "OK" : "NG")}，耗时：{dw3Watch.ElapsedMilliseconds}ms";
            Log(dw3LogMsg);
            summary.DetailedLogs.Add(dw3LogMsg);
            summary.DetailedLogs.Add($"点位3：校验【{(summary.Point3Result.IsOk ? "OK" : "NG")}】，时间【{datetimestr}】");


            // 获取Camera1的PinInfos中的两个angle
            var camera1Angles = new List<double>();
            if (summary.Camera1Result.PinInfos != null && summary.Camera1Result.PinInfos.Count > 0)
            {
                foreach (var pinInfo in summary.Camera1Result.PinInfos)
                {
                    camera1Angles.Add(pinInfo.Angle);
                }
                Log($"Camera1获取到{camera1Angles.Count}个角度：{string.Join(", ", camera1Angles.Select(a => a.ToString("F2")))}");
                summary.DetailedLogs.Add($"Camera1角度信息：{string.Join(", ", camera1Angles.Select(a => a.ToString("F2")))}");
            }

            // 获取Camera2的PinInfos中的一个angle和distance
            double camera2Angle = 0;
            double camera2Distance = 0;
            if (summary.Camera2Result.PinInfos != null && summary.Camera2Result.PinInfos.Count > 0)
            {
                var firstPinInfo = summary.Camera2Result.PinInfos.First();
                camera2Angle = firstPinInfo.Angle;
                camera2Distance = firstPinInfo.Distance;
                Log($"Camera2获取到角度：{camera2Angle:F2}，距离：{camera2Distance:F2}");
                summary.DetailedLogs.Add($"Camera2角度：{camera2Angle:F2}，距离：{camera2Distance:F2}");
            }

            // 【针脚倾斜】校验
            var pinTiltWatch = Stopwatch.StartNew();
            Log("【针脚倾斜】校验开始");
            summary.DetailedLogs.Add("【针脚倾斜】校验开始");

            bool isPinTiltOk = true;
            var tiltReasons = new List<string>();
            bool hasValidData = false;

            // 检查Camera1的角度（所有角度都要小于5度才算OK）
            if (camera1Angles.Count > 0)
            {
                hasValidData = true;
                var camera1TiltAngles = camera1Angles.Where(angle => angle < 5).ToList();
                if (camera1TiltAngles.Count != camera1Angles.Count)
                {
                    isPinTiltOk = false;
                    var failedAngles = camera1Angles.Where(angle => angle >= 5).ToList();
                    tiltReasons.Add($"Camera1角度异常：{string.Join(", ", failedAngles.Select(a => a.ToString("F2")))}度 >= 5度");
                }
                Log($"Camera1角度校验：{camera1TiltAngles.Count}/{camera1Angles.Count}个角度合格");
                summary.DetailedLogs.Add($"Camera1角度校验：{camera1TiltAngles.Count}/{camera1Angles.Count}个角度合格");
            }
            else
            {
                Log("Camera1未检测到角度信息");
                summary.DetailedLogs.Add("Camera1未检测到角度信息");
            }

            // 检查Camera2的角度（小于5度才算OK）
            if (camera2Angle > 0)
            {
                hasValidData = true;
                if (camera2Angle >= 5)
                {
                    isPinTiltOk = false;
                    tiltReasons.Add($"Camera2角度异常：{camera2Angle:F2}度 >= 5度");
                }
                Log($"Camera2角度校验：{camera2Angle:F2}度 {(camera2Angle < 5 ? "合格" : "不合格")}");
                summary.DetailedLogs.Add($"Camera2角度校验：{camera2Angle:F2}度 {(camera2Angle < 5 ? "合格" : "不合格")}");
            }
            else
            {
                Log("Camera2未检测到角度信息");
                summary.DetailedLogs.Add("Camera2未检测到角度信息");
            }

            // 检查Camera2的距离（在75-90之间才算OK）
            if (camera2Distance > 0)
            {
                hasValidData = true;
                if (camera2Distance < 75 || camera2Distance > 90)
                {
                    isPinTiltOk = false;
                    tiltReasons.Add($"Camera2距离异常：{camera2Distance:F2} 不在75-90范围内");
                }
                Log($"Camera2距离校验：{camera2Distance:F2} {(camera2Distance >= 75 && camera2Distance <= 90 ? "合格" : "不合格")}");
                summary.DetailedLogs.Add($"Camera2距离校验：{camera2Distance:F2} {(camera2Distance >= 75 && camera2Distance <= 90 ? "合格" : "不合格")}");
            }
            else
            {
                Log("Camera2未检测到距离信息");
                summary.DetailedLogs.Add("Camera2未检测到距离信息");
            }

            // 如果没有检测到任何有效数据，则判定为NG
            if (!hasValidData)
            {
                isPinTiltOk = false;
                tiltReasons.Add("未检测到有效的角度或距离数据");
                Log("针脚倾斜校验失败：未检测到有效数据");
                summary.DetailedLogs.Add("针脚倾斜校验失败：未检测到有效数据");
            }

            pinTiltWatch.Stop();
            summary.PinTiltResult.ProcessingTime = pinTiltWatch.ElapsedMilliseconds;
            summary.PinTiltResult.IsOk = isPinTiltOk;

            var pinTiltResult = isPinTiltOk ? "OK" : "NG";
            var pinTiltLogMsg = $"【针脚倾斜】校验结束，结果：{pinTiltResult}，耗时：{pinTiltWatch.ElapsedMilliseconds}ms";

            if (!isPinTiltOk)
            {
                pinTiltLogMsg += $"，原因：{string.Join("；", tiltReasons)}";
                summary.PinTiltResult.Message = string.Join("；", tiltReasons);

                // 将针脚倾斜的具体原因添加到NgReasons
                foreach (var reason in tiltReasons)
                {
                    summary.NgReasons.Add($"针脚倾斜：{reason}");
                }
            }
            else
            {
                if (hasValidData)
                {
                    summary.PinTiltResult.Message = "角度和距离检测正常";
                }
                else
                {
                    summary.PinTiltResult.Message = "未检测到角度或距离数据，跳过检测";
                }
            }

            Log(pinTiltLogMsg);
            summary.DetailedLogs.Add(pinTiltLogMsg);
            summary.DetailedLogs.Add($"针脚倾斜：校验【{pinTiltResult}】，时间【{datetimestr}】");

        }



        /// <summary>
        /// 日志输出方法（简化版，仅用于关键信息）
        /// </summary>
        private void Log(string message)
        {
            Logger.Debug(message); // 降级为Debug级别，减少生产环境日志量
        }

        /// <summary>
        /// 检查推理引擎状态
        /// </summary>
        public bool IsReady => _inferenceHelper?.IsInitialized == true && !_isDisposed;

        /// <summary>
        /// 启用性能优化模式（生产环境推荐）
        /// </summary>
        public void EnablePerformanceMode()
        {
            _inferenceHelper?.EnablePerformanceMode();
            Logger.Debug("图像处理管理器切换到性能优化模式");
        }

        /// <summary>
        /// 启用调试模式（用于性能分析）
        /// </summary>
        public void EnableDebugMode()
        {
            _inferenceHelper?.EnableDebugMode();
            Logger.Debug("图像处理管理器切换到调试模式");
        }

        /// <summary>
        /// 获取当前配置信息
        /// </summary>
        public string GetConfigInfo()
        {
            return _inferenceHelper?.GetConfigInfo() ?? "推理引擎未初始化";
        }

        /// <summary>
        /// 获取详细的检测结果报告
        /// </summary>
        public string GetDetailedResultReport(DetectionSummary summary)
        {
            if (summary == null) return "检测结果为空";

            var report = new System.Text.StringBuilder();
            report.AppendLine("=== 详细检测结果报告 ===");
            report.AppendLine($"整体结果：{(summary.OverallResult ? "PASS" : "FAIL")}");
            report.AppendLine($"总处理耗时：{summary.TotalProcessingTime:F2}ms");
            report.AppendLine();

            // 相机检测结果
            report.AppendLine("--- 相机检测结果 ---");
            report.AppendLine($"相机1检测数量：{summary.Camera1Result?.Detections?.Count ?? 0}");
            report.AppendLine($"相机2检测数量：{summary.Camera2Result?.Detections?.Count ?? 0}");
            report.AppendLine();

            // 点位检测结果
            report.AppendLine("--- 点位检测结果 ---");
            report.AppendLine($"点位1：{(summary.Point1Result.IsOk ? "OK" : "NG")} (耗时：{summary.Point1Result.ProcessingTime}ms)");
            if (!string.IsNullOrEmpty(summary.Point1Result.Message))
                report.AppendLine($"  消息：{summary.Point1Result.Message}");

            report.AppendLine($"点位2：{(summary.Point2Result.IsOk ? "OK" : "NG")} (耗时：{summary.Point2Result.ProcessingTime}ms)");
            if (!string.IsNullOrEmpty(summary.Point2Result.Message))
                report.AppendLine($"  消息：{summary.Point2Result.Message}");

            report.AppendLine($"点位3：{(summary.Point3Result.IsOk ? "OK" : "NG")} (耗时：{summary.Point3Result.ProcessingTime}ms)");
            if (!string.IsNullOrEmpty(summary.Point3Result.Message))
                report.AppendLine($"  消息：{summary.Point3Result.Message}");

            report.AppendLine($"针脚倾斜：{(summary.PinTiltResult.IsOk ? "OK" : "NG")} (耗时：{summary.PinTiltResult.ProcessingTime}ms)");
            if (!string.IsNullOrEmpty(summary.PinTiltResult.Message))
                report.AppendLine($"  消息：{summary.PinTiltResult.Message}");

            // NG原因汇总
            if (summary.NgReasons.Count > 0)
            {
                report.AppendLine();
                report.AppendLine("--- NG原因汇总 ---");
                foreach (var reason in summary.NgReasons)
                {
                    report.AppendLine($"• {reason}");
                }
            }

            // 详细日志
            if (summary.DetailedLogs.Any())
            {
                report.AppendLine();
                report.AppendLine("--- 详细日志 ---");
                foreach (var log in summary.DetailedLogs)
                {
                    report.AppendLine(log);
                }
            }

            report.AppendLine("========================");
            return report.ToString();
        }

        public void Dispose()
        {
            if (!_isDisposed)
            {
                _inferenceHelper?.Dispose();
                _isDisposed = true;
                Logger.Info("图像处理管理器已释放");
            }
        }
    }
}

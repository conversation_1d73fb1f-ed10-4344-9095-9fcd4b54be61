using MQTTnet.Protocol;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace GGEC.AOI.T68ZJ.Client.Helper
{
    /// <summary>
    /// MQTT控制消息助手类
    /// 负责构建和发送标准化的MQTT控制消息
    /// </summary>
    public static class MqttControlMessageHelper
    {
        #region 常量定义
        
        /// <summary>
        /// 默认协议版本
        /// </summary>
        public const string DEFAULT_VERSION = "1.0.1";
        
        /// <summary>
        /// 默认方向
        /// </summary>
        public const string DEFAULT_DIRECTION = "down";
        
        /// <summary>
        /// 默认消息ID
        /// </summary>
        public const string DEFAULT_MESSAGE_ID = "1";
        
        /// <summary>
        /// 默认主题
        /// </summary>
        public const string DEFAULT_TOPIC = "/DownloadTopic";
        
        /// <summary>
        /// 检测失败控制消息延时（毫秒）
        /// </summary>
        public const int DETECTION_FAILURE_DELAY_MS = 1500;
        
        #endregion

        #region 消息数据结构

        /// <summary>
        /// MQTT控制消息根对象
        /// </summary>
        public class MqttControlMessage
        {
            [JsonProperty("rw_prot")]
            public RwProtocol RwProtocol { get; set; }
        }

        /// <summary>
        /// 读写协议对象
        /// </summary>
        public class RwProtocol
        {
            [JsonProperty("Ver")]
            public string Version { get; set; } = DEFAULT_VERSION;

            [JsonProperty("dir")]
            public string Direction { get; set; } = DEFAULT_DIRECTION;

            [JsonProperty("id")]
            public string Id { get; set; } = DEFAULT_MESSAGE_ID;

            [JsonProperty("r_data")]
            public IoData[] ReadData { get; set; }

            [JsonProperty("w_data")]
            public IoData[] WriteData { get; set; }
        }

        /// <summary>
        /// IO数据对象
        /// </summary>
        public class IoData
        {
            [JsonProperty("name")]
            public string Name { get; set; }

            [JsonProperty("value")]
            public string Value { get; set; }
        }

        #endregion

        #region 消息构建方法

        /// <summary>
        /// 创建检测失败控制消息（DO1=1, DO2=1）
        /// </summary>
        /// <returns>JSON格式的控制消息</returns>
        public static string CreateDetectionFailureMessage()
        {
            var message = new MqttControlMessage
            {
                RwProtocol = new RwProtocol
                {
                    ReadData = new[]
                    {
                        new IoData { Name = "DO1" }
                    },
                    WriteData = new[]
                    {
                        new IoData { Name = "DO1", Value = "1" },
                        new IoData { Name = "DO2", Value = "1" }
                    }
                }
            };

            return JsonConvert.SerializeObject(message, Formatting.None);
        }

        /// <summary>
        /// 创建检测失败恢复消息（DO1=1, DO2=0）
        /// </summary>
        /// <returns>JSON格式的控制消息</returns>
        public static string CreateDetectionFailureRecoveryMessage()
        {
            var message = new MqttControlMessage
            {
                RwProtocol = new RwProtocol
                {
                    ReadData = new[]
                    {
                        new IoData { Name = "DO1" }
                    },
                    WriteData = new[]
                    {
                        new IoData { Name = "DO1", Value = "1" },
                        new IoData { Name = "DO2", Value = "0" }
                    }
                }
            };

            return JsonConvert.SerializeObject(message, Formatting.None);
        }

        /// <summary>
        /// 创建自定义控制消息
        /// </summary>
        /// <param name="readDataNames">读取数据名称数组</param>
        /// <param name="writeData">写入数据数组</param>
        /// <param name="version">协议版本</param>
        /// <param name="messageId">消息ID</param>
        /// <returns>JSON格式的控制消息</returns>
        public static string CreateCustomControlMessage(
            string[] readDataNames,
            (string name, string value)[] writeData,
            string version = DEFAULT_VERSION,
            string messageId = DEFAULT_MESSAGE_ID)
        {
            var readDataArray = readDataNames?.Select(name => new IoData { Name = name }).ToArray() ?? new IoData[0];
            var writeDataArray = writeData?.Select(item => new IoData { Name = item.name, Value = item.value }).ToArray() ?? new IoData[0];

            var message = new MqttControlMessage
            {
                RwProtocol = new RwProtocol
                {
                    Version = version,
                    Id = messageId,
                    ReadData = readDataArray,
                    WriteData = writeDataArray
                }
            };

            return JsonConvert.SerializeObject(message, Formatting.None);
        }

        #endregion

        #region 消息发送方法

        /// <summary>
        /// 发送检测失败控制消息序列
        /// </summary>
        /// <param name="mqttManager">MQTT管理器</param>
        /// <param name="topic">主题，默认为 "/DownloadTopic"</param>
        /// <param name="qos">QoS级别</param>
        /// <returns>发送结果</returns>
        public static async Task<(bool firstSuccess, bool secondSuccess)> SendDetectionFailureControlSequenceAsync(
            Mqtt.SimpleMqttManager mqttManager,
            string topic = DEFAULT_TOPIC,
            MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtLeastOnce)
        {
            if (mqttManager == null)
            {
                throw new ArgumentNullException(nameof(mqttManager));
            }

            try
            {
                // 发送第一条消息（DO1=1, DO2=1）
                var firstMessage = CreateDetectionFailureMessage();
                var firstResult = await mqttManager.PublishAsync(topic, firstMessage, qos);

                // 延时3秒
                await Task.Delay(DETECTION_FAILURE_DELAY_MS);

                // 发送第二条消息（DO1=1, DO2=0）
                var secondMessage = CreateDetectionFailureRecoveryMessage();
                var secondResult = await mqttManager.PublishAsync(topic, secondMessage, qos);

                return (firstResult, secondResult);
            }
            catch (Exception ex)
            {
                // 这里可以记录日志或抛出自定义异常
                throw new InvalidOperationException($"发送检测失败控制消息序列时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 发送单个控制消息
        /// </summary>
        /// <param name="mqttManager">MQTT管理器</param>
        /// <param name="message">消息内容</param>
        /// <param name="topic">主题</param>
        /// <param name="qos">QoS级别</param>
        /// <returns>发送结果</returns>
        public static async Task<bool> SendControlMessageAsync(
            Mqtt.SimpleMqttManager mqttManager,
            string message,
            string topic = DEFAULT_TOPIC,
            MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtLeastOnce)
        {
            if (mqttManager == null)
            {
                throw new ArgumentNullException(nameof(mqttManager));
            }

            if (string.IsNullOrWhiteSpace(message))
            {
                throw new ArgumentException("消息内容不能为空", nameof(message));
            }

            try
            {
                return await mqttManager.PublishAsync(topic, message, qos);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"发送MQTT控制消息时发生错误: {ex.Message}", ex);
            }
        }

        #endregion
    }
}

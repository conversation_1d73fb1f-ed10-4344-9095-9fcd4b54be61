using MvCameraControl;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Windows.Forms;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using GGEC.AOI.T68ZJ.Log;
using GGEC.AOI.T68ZJ.Config;

namespace GGEC.AOI.T68ZJ.Managers
{
    /// <summary>
    /// CameraManager2 - 参考 BasicDemoLineScan 实现的相机管理器
    /// 提供更直接的视频预览和拍照功能，减少复杂的缓存机制
    /// </summary>
    public class CameraManager2
    {
        #region 私有字段

        // 设备层类型枚举
        readonly DeviceTLayerType enumTLayerType = DeviceTLayerType.MvGigEDevice | DeviceTLayerType.MvUsbDevice
                | DeviceTLayerType.MvGenTLGigEDevice | DeviceTLayerType.MvGenTLCXPDevice 
                | DeviceTLayerType.MvGenTLCameraLinkDevice | DeviceTLayerType.MvGenTLXoFDevice;

        // 设备管理
        private List<IDeviceInfo> deviceInfos = new List<IDeviceInfo>();
        private List<IDevice> devices = new List<IDevice>();
        
        // 预览线程管理
        private List<Thread> receiveThreads = new List<Thread>();
        private List<bool> isGrabbingFlags = new List<bool>();
        private readonly object threadLock = new object();

        // 图像保存相关
        private List<IFrameOut> framesForSave = new List<IFrameOut>();
        private readonly object lockForSaveImage = new object();

        // 预览控件
        private List<PictureBox> previewPictureBoxes = new List<PictureBox>();

        // 单例模式
        private static CameraManager2? _instance;

        #endregion

        #region 公共属性

        /// <summary>
        /// 曝光时间（微秒）
        /// </summary>
        public double ExposureTime { get; set; } = 2000.0;

        /// <summary>
        /// 增益值
        /// </summary>
        public double Gain { get; set; } = 20.0;

        /// <summary>
        /// 像素格式
        /// </summary>
        public string PixelFormat { get; set; } = "Mono8";

        /// <summary>
        /// 是否启用自动曝光
        /// </summary>
        public bool AutoExposureEnabled { get; set; } = false;

        /// <summary>
        /// 是否启用自动增益
        /// </summary>
        public bool AutoGainEnabled { get; set; } = false;

        /// <summary>
        /// 预览状态
        /// </summary>
        public bool ShowLivePreview { get; set; } = false;

        /// <summary>
        /// 设备数量
        /// </summary>
        public int DeviceCount => devices.Count;

        /// <summary>
        /// 是否正在采集
        /// </summary>
        public bool IsGrabbing => isGrabbingFlags.Count > 0 && isGrabbingFlags.TrueForAll(flag => flag);

        #endregion

        #region 单例模式

        private CameraManager2()
        {
            Logger.Info("CameraManager2 初始化完成");
        }

        public static CameraManager2 Instance
        {
            get
            {
                _instance ??= new CameraManager2();
                return _instance;
            }
        }

        /// <summary>
        /// 从配置文件加载参数
        /// </summary>
        public void LoadConfigFromAppConfig()
        {
            try
            {
                ExposureTime = AppConfig.Camera.ExposureTime;
                Gain = AppConfig.Camera.Gain;
                PixelFormat = AppConfig.Camera.PixelFormat;
                AutoExposureEnabled = AppConfig.Camera.AutoExposureEnabled;
                AutoGainEnabled = AppConfig.Camera.AutoGainEnabled;

                Logger.Info($"CameraManager2配置已加载 - 曝光时间: {ExposureTime}, 增益: {Gain}, 像素格式: {PixelFormat}");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "加载CameraManager2配置失败，使用默认值");
            }
        }

        #endregion

        #region 设备管理

        /// <summary>
        /// 初始化相机系统
        /// </summary>
        public void InitCameras()
        {
            try
            {
                Logger.Info("CameraManager2 开始初始化相机系统");
                
                // 初始化SDK
                SDKSystem.Initialize();
                
                // 枚举设备
                UpdateDeviceList();
                
                Logger.Info($"CameraManager2 相机系统初始化完成，发现 {deviceInfos.Count} 个设备");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "CameraManager2 初始化相机系统失败");
                throw;
            }
        }

        /// <summary>
        /// 更新设备列表
        /// </summary>
        private void UpdateDeviceList()
        {
            deviceInfos.Clear();
            
            int result = DeviceEnumerator.EnumDevices(enumTLayerType, out deviceInfos);
            if (result != MvError.MV_OK)
            {
                string errorMsg = GetErrorMessage(result);
                Logger.Error($"枚举设备失败: {errorMsg}");
                throw new Exception($"枚举设备失败: {errorMsg}");
            }

            Logger.Info($"发现 {deviceInfos.Count} 个设备:");
            for (int i = 0; i < deviceInfos.Count; i++)
            {
                var deviceInfo = deviceInfos[i];
                string deviceName = !string.IsNullOrEmpty(deviceInfo.UserDefinedName) 
                    ? $"{deviceInfo.UserDefinedName} ({deviceInfo.SerialNumber})"
                    : $"{deviceInfo.ManufacturerName} {deviceInfo.ModelName} ({deviceInfo.SerialNumber})";
                Logger.Info($"  设备 {i}: {deviceName}");
            }
        }

        /// <summary>
        /// 打开指定数量的设备
        /// </summary>
        public void OpenDevices(int deviceCount = 2)
        {
            try
            {
                Logger.Info($"CameraManager2 开始打开 {deviceCount} 个设备");
                
                if (deviceInfos.Count < deviceCount)
                {
                    throw new Exception($"可用设备数量不足，需要 {deviceCount} 个，实际发现 {deviceInfos.Count} 个");
                }

                // 关闭已打开的设备
                CloseDevices();

                // 打开设备
                for (int i = 0; i < deviceCount; i++)
                {
                    OpenSingleDevice(i);
                }

                Logger.Info($"CameraManager2 成功打开 {devices.Count} 个设备");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "CameraManager2 打开设备失败");
                CloseDevices(); // 清理已打开的设备
                throw;
            }
        }

        /// <summary>
        /// 打开单个设备
        /// </summary>
        private void OpenSingleDevice(int deviceIndex)
        {
            var deviceInfo = deviceInfos[deviceIndex];
            
            try
            {
                // 创建设备
                IDevice device = DeviceFactory.CreateDevice(deviceInfo);
                if (device == null)
                {
                    throw new Exception($"创建设备失败：{deviceInfo.SerialNumber}");
                }

                // 打开设备
                int result = device.Open();
                if (result != MvError.MV_OK)
                {
                    device.Dispose();
                    string errorMsg = GetErrorMessage(result);
                    throw new Exception($"打开设备失败：{deviceInfo.SerialNumber}，错误：{errorMsg}");
                }

                // 配置设备参数
                ConfigureDevice(device, deviceInfo);

                // 添加到设备列表
                devices.Add(device);
                isGrabbingFlags.Add(false);
                framesForSave.Add(null);

                Logger.Info($"设备 {deviceIndex} ({deviceInfo.SerialNumber}) 打开成功");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, $"打开设备 {deviceIndex} 失败");
                throw;
            }
        }

        /// <summary>
        /// 配置设备参数
        /// </summary>
        private void ConfigureDevice(IDevice device, IDeviceInfo deviceInfo)
        {
            try
            {
                Logger.Info($"配置设备参数：{deviceInfo.SerialNumber}");

                // GigE设备网络优化
                if (device is IGigEDevice gigEDevice)
                {
                    int optimalPacketSize;
                    int result = gigEDevice.GetOptimalPacketSize(out optimalPacketSize);
                    if (result == MvError.MV_OK)
                    {
                        result = device.Parameters.SetIntValue("GevSCPSPacketSize", optimalPacketSize);
                        if (result == MvError.MV_OK)
                        {
                            Logger.Info($"设置包大小为：{optimalPacketSize}");
                        }
                    }
                }

                // 设置采集模式
                device.Parameters.SetEnumValueByString("AcquisitionMode", "Continuous");
                device.Parameters.SetEnumValueByString("TriggerMode", "Off");

                // 设置像素格式
                try
                {
                    device.Parameters.SetEnumValueByString("PixelFormat", PixelFormat);
                    Logger.Info($"设置像素格式为：{PixelFormat}");
                }
                catch (Exception ex)
                {
                    Logger.Warn($"设置像素格式失败，使用默认格式：{ex.Message}");
                }

                // 设置曝光参数
                ConfigureExposureAndGain(device);

                Logger.Info($"设备参数配置完成：{deviceInfo.SerialNumber}");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, $"配置设备参数失败：{deviceInfo.SerialNumber}");
                // 配置失败不抛出异常，使用默认参数
            }
        }

        /// <summary>
        /// 配置曝光和增益参数
        /// </summary>
        private void ConfigureExposureAndGain(IDevice device)
        {
            try
            {
                if (AutoExposureEnabled)
                {
                    device.Parameters.SetEnumValueByString("ExposureAuto", "Continuous");
                    Logger.Info("启用自动曝光");
                }
                else
                {
                    device.Parameters.SetEnumValueByString("ExposureAuto", "Off");
                    int result = device.Parameters.SetFloatValue("ExposureTime", (float)ExposureTime);
                    if (result == MvError.MV_OK)
                    {
                        Logger.Info($"设置曝光时间为：{ExposureTime} 微秒");
                    }
                }

                if (AutoGainEnabled)
                {
                    device.Parameters.SetEnumValueByString("GainAuto", "Continuous");
                    Logger.Info("启用自动增益");
                }
                else
                {
                    device.Parameters.SetEnumValueByString("GainAuto", "Off");
                    int result = device.Parameters.SetFloatValue("Gain", (float)Gain);
                    if (result == MvError.MV_OK)
                    {
                        Logger.Info($"设置增益为：{Gain}");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "配置曝光和增益参数失败");
            }
        }

        #endregion

        #region 错误处理

        /// <summary>
        /// 获取错误信息
        /// </summary>
        private string GetErrorMessage(int errorCode)
        {
            return errorCode switch
            {
                MvError.MV_E_HANDLE => "Error or invalid handle",
                MvError.MV_E_SUPPORT => "Not supported function",
                MvError.MV_E_BUFOVER => "Cache is full",
                MvError.MV_E_CALLORDER => "Function calling order error",
                MvError.MV_E_PARAMETER => "Incorrect parameter",
                MvError.MV_E_RESOURCE => "Applying resource failed",
                MvError.MV_E_NODATA => "No data",
                MvError.MV_E_PRECONDITION => "Precondition error, or running environment changed",
                MvError.MV_E_VERSION => "Version mismatches",
                MvError.MV_E_NOENOUGH_BUF => "Insufficient memory",
                MvError.MV_E_UNKNOW => "Unknown error",
                MvError.MV_E_GC_GENERIC => "General error",
                MvError.MV_E_GC_ACCESS => "Node accessing condition error",
                MvError.MV_E_ACCESS_DENIED => "No permission",
                MvError.MV_E_BUSY => "Device is busy, or network disconnected",
                MvError.MV_E_NETER => "Network error",
                _ => $"Error code: 0x{errorCode:X8}"
            };
        }

        #endregion

        #region 视频预览功能

        /// <summary>
        /// 开始实时预览
        /// </summary>
        public void StartLivePreview(params PictureBox[] pictureBoxes)
        {
            try
            {
                Logger.Info("CameraManager2 开始启动实时预览");

                if (devices.Count == 0)
                {
                    throw new Exception("没有可用的设备，请先打开设备");
                }

                if (pictureBoxes.Length < devices.Count)
                {
                    throw new Exception($"PictureBox数量不足，需要 {devices.Count} 个，提供了 {pictureBoxes.Length} 个");
                }

                // 停止现有预览
                StopLivePreview();

                // 保存预览控件
                previewPictureBoxes.Clear();
                previewPictureBoxes.AddRange(pictureBoxes);

                // 启动采集和预览线程
                lock (threadLock)
                {
                    for (int i = 0; i < devices.Count; i++)
                    {
                        StartGrabbingForDevice(i);
                    }
                }

                ShowLivePreview = true;
                Logger.Info($"CameraManager2 实时预览已启动，{devices.Count} 个设备");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "CameraManager2 启动实时预览失败");
                throw;
            }
        }

        /// <summary>
        /// 停止实时预览
        /// </summary>
        public void StopLivePreview()
        {
            try
            {
                Logger.Info("CameraManager2 停止实时预览");

                ShowLivePreview = false;

                lock (threadLock)
                {
                    // 停止所有采集
                    for (int i = 0; i < devices.Count; i++)
                    {
                        StopGrabbingForDevice(i);
                    }

                    // 等待所有线程结束
                    foreach (var thread in receiveThreads)
                    {
                        if (thread != null && thread.IsAlive)
                        {
                            thread.Join(1000); // 最多等待1秒
                        }
                    }

                    receiveThreads.Clear();
                }

                Logger.Info("CameraManager2 实时预览已停止");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "CameraManager2 停止实时预览失败");
            }
        }

        /// <summary>
        /// 为指定设备启动采集
        /// </summary>
        private void StartGrabbingForDevice(int deviceIndex)
        {
            try
            {
                if (deviceIndex >= devices.Count)
                {
                    Logger.Error($"设备索引超出范围：{deviceIndex}");
                    return;
                }

                var device = devices[deviceIndex];

                // 启动采集
                int result = device.StreamGrabber.StartGrabbing();
                if (result != MvError.MV_OK)
                {
                    string errorMsg = GetErrorMessage(result);
                    Logger.Error($"设备 {deviceIndex} 启动采集失败：{errorMsg}");
                    return;
                }

                // 设置采集标志
                isGrabbingFlags[deviceIndex] = true;

                // 启动接收线程
                Thread receiveThread = new Thread(() => ReceiveThreadProcess(deviceIndex))
                {
                    Name = $"ReceiveThread_{deviceIndex}",
                    IsBackground = true
                };

                // 确保线程列表有足够的空间
                while (receiveThreads.Count <= deviceIndex)
                {
                    receiveThreads.Add(null);
                }
                receiveThreads[deviceIndex] = receiveThread;

                receiveThread.Start();

                Logger.Info($"设备 {deviceIndex} 采集已启动");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, $"启动设备 {deviceIndex} 采集失败");
            }
        }

        /// <summary>
        /// 为指定设备停止采集
        /// </summary>
        private void StopGrabbingForDevice(int deviceIndex)
        {
            try
            {
                if (deviceIndex >= devices.Count)
                {
                    return;
                }

                // 设置停止标志
                if (deviceIndex < isGrabbingFlags.Count)
                {
                    isGrabbingFlags[deviceIndex] = false;
                }

                // 等待线程结束
                if (deviceIndex < receiveThreads.Count && receiveThreads[deviceIndex] != null)
                {
                    receiveThreads[deviceIndex].Join(1000);
                }

                // 停止设备采集
                var device = devices[deviceIndex];
                int result = device.StreamGrabber.StopGrabbing();
                if (result != MvError.MV_OK)
                {
                    string errorMsg = GetErrorMessage(result);
                    Logger.Warn($"设备 {deviceIndex} 停止采集失败：{errorMsg}");
                }

                Logger.Info($"设备 {deviceIndex} 采集已停止");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, $"停止设备 {deviceIndex} 采集失败");
            }
        }

        /// <summary>
        /// 接收图像线程处理 - 参考 BasicDemoLineScan 实现
        /// </summary>
        private void ReceiveThreadProcess(int deviceIndex)
        {
            Logger.Info($"设备 {deviceIndex} 接收线程开始运行");

            IFrameOut frameOut = null;
            var device = devices[deviceIndex];

            while (deviceIndex < isGrabbingFlags.Count && isGrabbingFlags[deviceIndex])
            {
                try
                {
                    int result = device.StreamGrabber.GetImageBuffer(1000, out frameOut);
                    if (result == MvError.MV_OK && frameOut?.Image != null)
                    {
                        // 保存图像数据用于拍照
                        lock (lockForSaveImage)
                        {
                            try
                            {
                                // 释放旧的帧数据
                                framesForSave[deviceIndex]?.Dispose();
                                framesForSave[deviceIndex] = frameOut.Clone() as IFrameOut;
                            }
                            catch (Exception ex)
                            {
                                Logger.Exception(ex, $"设备 {deviceIndex} 克隆帧数据失败");
                            }
                        }

                        // 渲染图像到预览控件
                        if (ShowLivePreview && deviceIndex < previewPictureBoxes.Count)
                        {
                            var pictureBox = previewPictureBoxes[deviceIndex];
                            if (pictureBox != null && !pictureBox.IsDisposed)
                            {
                                try
                                {
                                    // 使用 ImageRender 直接渲染到 PictureBox
                                    device.ImageRender.DisplayOneFrame(pictureBox.Handle, frameOut.Image);
                                }
                                catch (Exception ex)
                                {
                                    Logger.Exception(ex, $"设备 {deviceIndex} 渲染图像失败");
                                }
                            }
                        }

                        // 释放帧信息
                        device.StreamGrabber.FreeImageBuffer(frameOut);
                        frameOut = null;
                    }
                    else if (result != MvError.MV_OK)
                    {
                        // 获取图像失败，短暂休眠
                        Thread.Sleep(5);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Exception(ex, $"设备 {deviceIndex} 接收线程发生异常");
                    Thread.Sleep(100);
                }
            }

            // 清理资源
            frameOut?.Dispose();
            Logger.Info($"设备 {deviceIndex} 接收线程已停止");
        }

        #endregion

        #region 拍照功能

        /// <summary>
        /// 拍摄单张照片
        /// </summary>
        public IImage? TakePhoto(int deviceIndex)
        {
            try
            {
                if (deviceIndex >= devices.Count)
                {
                    Logger.Error($"设备索引超出范围：{deviceIndex}");
                    return null;
                }

                Logger.Info($"设备 {deviceIndex} 开始拍照");

                lock (lockForSaveImage)
                {
                    if (deviceIndex < framesForSave.Count && framesForSave[deviceIndex]?.Image != null)
                    {
                        var image = framesForSave[deviceIndex].Image;
                        Logger.Info($"设备 {deviceIndex} 拍照成功，图像尺寸：{image.Width}x{image.Height}");
                        return image;
                    }
                    else
                    {
                        Logger.Warn($"设备 {deviceIndex} 没有可用的图像数据");
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, $"设备 {deviceIndex} 拍照失败");
                return null;
            }
        }

        /// <summary>
        /// 同时拍摄所有设备的照片
        /// </summary>
        public List<IImage?> TakePhotos()
        {
            var images = new List<IImage?>();

            try
            {
                Logger.Info("开始拍摄所有设备照片");

                lock (lockForSaveImage)
                {
                    for (int i = 0; i < devices.Count; i++)
                    {
                        if (i < framesForSave.Count && framesForSave[i]?.Image != null)
                        {
                            images.Add(framesForSave[i].Image);
                        }
                        else
                        {
                            images.Add(null);
                        }
                    }
                }

                Logger.Info($"拍摄完成，成功获取 {images.Count(img => img != null)} 张图像");
                return images;
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "拍摄所有设备照片失败");
                return images;
            }
        }

        /// <summary>
        /// 传感器触发拍照 - 兼容性方法
        /// </summary>
        public (IImage? image1, IImage? image2) TakeSensorTriggeredPhotos()
        {
            try
            {
                Logger.Info("传感器触发拍照");

                var photos = TakePhotos();
                var image1 = photos.Count > 0 ? photos[0] : null;
                var image2 = photos.Count > 1 ? photos[1] : null;

                Logger.Info($"传感器触发拍照完成，相机1：{(image1 != null ? "成功" : "失败")}，相机2：{(image2 != null ? "成功" : "失败")}");
                return (image1, image2);
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "传感器触发拍照失败");
                return (null, null);
            }
        }

        /// <summary>
        /// 保存图像到文件
        /// </summary>
        public int SaveImage(int deviceIndex, ImageFormatInfo imageFormatInfo, string? customPath = null)
        {
            try
            {
                lock (lockForSaveImage)
                {
                    if (deviceIndex >= framesForSave.Count || framesForSave[deviceIndex]?.Image == null)
                    {
                        Logger.Error($"设备 {deviceIndex} 没有可保存的图像");
                        return MvError.MV_E_NODATA;
                    }

                    var frameForSave = framesForSave[deviceIndex];
                    string imagePath = customPath ?? GenerateImagePath(deviceIndex, frameForSave, imageFormatInfo);

                    var device = devices[deviceIndex];
                    int result = device.ImageSaver.SaveImageToFile(imagePath, frameForSave.Image, imageFormatInfo, CFAMethod.Equilibrated);

                    if (result == MvError.MV_OK)
                    {
                        Logger.Info($"设备 {deviceIndex} 图像保存成功：{imagePath}");
                    }
                    else
                    {
                        Logger.Error($"设备 {deviceIndex} 图像保存失败：{GetErrorMessage(result)}");
                    }

                    return result;
                }
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, $"设备 {deviceIndex} 保存图像时发生异常");
                return MvError.MV_E_UNKNOW;
            }
        }

        /// <summary>
        /// 生成图像文件路径
        /// </summary>
        private string GenerateImagePath(int deviceIndex, IFrameOut frameForSave, ImageFormatInfo imageFormatInfo)
        {
            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss_fff");
            string extension = imageFormatInfo.FormatType.ToString().ToLower();

            // 确保输出目录存在
            string outputDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Images", DateTime.Now.ToString("yyyyMMdd"));
            Directory.CreateDirectory(outputDir);

            return Path.Combine(outputDir,
                $"Camera{deviceIndex}_w{frameForSave.Image.Width}_h{frameForSave.Image.Height}_fn{frameForSave.FrameNum}_{timestamp}.{extension}");
        }

        /// <summary>
        /// 保存BMP格式图像
        /// </summary>
        public int SaveBmpImage(int deviceIndex, string? customPath = null)
        {
            var imageFormatInfo = new ImageFormatInfo { FormatType = ImageFormatType.Bmp };
            return SaveImage(deviceIndex, imageFormatInfo, customPath);
        }

        /// <summary>
        /// 保存JPEG格式图像
        /// </summary>
        public int SaveJpegImage(int deviceIndex, int quality = 80, string? customPath = null)
        {
            var imageFormatInfo = new ImageFormatInfo
            {
                FormatType = ImageFormatType.Jpeg,
                JpegQuality = (uint)quality
            };
            return SaveImage(deviceIndex, imageFormatInfo, customPath);
        }

        /// <summary>
        /// 保存PNG格式图像
        /// </summary>
        public int SavePngImage(int deviceIndex, string? customPath = null)
        {
            var imageFormatInfo = new ImageFormatInfo { FormatType = ImageFormatType.Png };
            return SaveImage(deviceIndex, imageFormatInfo, customPath);
        }

        #endregion

        #region 设备关闭和清理

        /// <summary>
        /// 关闭所有设备
        /// </summary>
        public void CloseDevices()
        {
            try
            {
                Logger.Info("CameraManager2 开始关闭所有设备");

                // 停止预览
                StopLivePreview();

                // 关闭设备
                lock (threadLock)
                {
                    for (int i = 0; i < devices.Count; i++)
                    {
                        try
                        {
                            var device = devices[i];
                            if (device != null)
                            {
                                device.Close();
                                device.Dispose();
                                Logger.Info($"设备 {i} 已关闭");
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Exception(ex, $"关闭设备 {i} 时发生异常");
                        }
                    }

                    // 清理资源
                    devices.Clear();
                    isGrabbingFlags.Clear();
                    receiveThreads.Clear();
                    previewPictureBoxes.Clear();

                    // 清理保存的帧数据
                    lock (lockForSaveImage)
                    {
                        foreach (var frame in framesForSave)
                        {
                            frame?.Dispose();
                        }
                        framesForSave.Clear();
                    }
                }

                Logger.Info("CameraManager2 所有设备已关闭");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "CameraManager2 关闭设备时发生异常");
            }
        }

        /// <summary>
        /// 释放资源并关闭SDK
        /// </summary>
        public void Dispose()
        {
            try
            {
                Logger.Info("CameraManager2 开始释放资源");

                CloseDevices();

                // 关闭SDK
                SDKSystem.Finalize();

                Logger.Info("CameraManager2 资源释放完成");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "CameraManager2 释放资源时发生异常");
            }
        }

        #endregion

        #region 兼容性方法

        /// <summary>
        /// 异步传感器触发拍照 - 兼容性方法
        /// </summary>
        public async Task<(IImage? image1, IImage? image2)> TakeSensorTriggeredPhotosAsync()
        {
            return await Task.Run(() => TakeSensorTriggeredPhotos());
        }

        /// <summary>
        /// 获取内存使用情况 - 兼容性方法
        /// </summary>
        public string GetMemoryUsage()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                long memoryBytes = process.WorkingSet64;
                double memoryMB = memoryBytes / (1024.0 * 1024.0);
                return $"{memoryMB:F1} MB";
            }
            catch
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// 自动调整传送带参数 - 兼容性方法
        /// </summary>
        public void AutoAdjustForConveyorSpeed()
        {
            Logger.Info("CameraManager2 自动调整传送带参数（兼容性方法）");
            // 简化实现，不做实际调整
        }

        /// <summary>
        /// 设置传送带速度 - 兼容性属性
        /// </summary>
        public double ConveyorBeltSpeed { get; set; } = 0.3;

        /// <summary>
        /// 传感器触发延迟补偿 - 兼容性属性
        /// </summary>
        public int SensorTriggerDelayCompensation { get; set; } = 50;

        /// <summary>
        /// 最小拍照间隔 - 兼容性属性
        /// </summary>
        public int MinPhotoIntervalMs { get; set; } = 10;

        #endregion
    }
}

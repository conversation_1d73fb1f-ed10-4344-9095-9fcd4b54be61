//using GGEC.AOI.T68ZJ.Managers;
//using System.Collections.Generic;
//using System.IO;
//using System.Text.Json;

//namespace GGEC.AOI.T68ZJ.Config
//{
//    /// <summary>
//    /// 简化的配置管理器
//    /// </summary>
//    public static class SimpleConfig
//    {
//        private static readonly string ConfigPath = "Config/inference_config.json";

//        /// <summary>
//        /// 加载配置
//        /// </summary>
//        public static InferenceConfig LoadConfig()
//        {
//            try
//            {
//                if (File.Exists(ConfigPath))
//                {
//                    var json = File.ReadAllText(ConfigPath);
//                    return JsonSerializer.Deserialize<InferenceConfig>(json) ?? new InferenceConfig();
//                }
//                else
//                {
//                    // 创建默认配置
//                    var defaultConfig = new InferenceConfig();
//                    SaveConfig(defaultConfig);
//                    return defaultConfig;
//                }
//            }
//            catch
//            {
//                return new InferenceConfig();
//            }
//        }

//        /// <summary>
//        /// 保存配置
//        /// </summary>
//        public static void SaveConfig(InferenceConfig config)
//        {
//            try
//            {
//                var directory = Path.GetDirectoryName(ConfigPath);
//                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
//                {
//                    Directory.CreateDirectory(directory);
//                }

//                var json = JsonSerializer.Serialize(config, new JsonSerializerOptions
//                {
//                    WriteIndented = true
//                });
//                File.WriteAllText(ConfigPath, json);
//            }
//            catch
//            {
//                // 忽略保存错误
//            }
//        }
//    }
//}

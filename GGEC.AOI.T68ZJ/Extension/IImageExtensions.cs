﻿using MvCameraControl;
using OpenCvSharp;
using System.Runtime.InteropServices;

namespace GGEC.AOI.T68ZJ.Extension
{
    public static class IImageExtensions
    {
        public static Mat ImageToMat(this IImage image)
        {
            if (image == null)
                throw new ArgumentNullException("image 为空");

            byte[] buffer = image.PixelData;
            int width = (int)image.Width;
            int height = (int)image.Height;

            if (buffer.Length < width * height)
                throw new Exception($"图像数据长度不足，期望至少 {width * height} 字节，但实际为 {buffer.Length}");

            // Step 1: 先构建单通道的 Bayer 原始图像 Mat（灰度图）
            var bayerMat = new Mat(height, width, MatType.CV_8UC1);
            Marshal.Copy(buffer, 0, bayerMat.Data, width * height);

            // Step 2: BayerRG8 转换为 BGR 彩色图像
            var colorMat = new Mat();
            Cv2.CvtColor(bayerMat, colorMat, ColorConversionCodes.BayerRG2BGR);

            // 释放临时的 Bayer Mat
            bayerMat.Dispose();

            return colorMat;
        }

        /// <summary>
        /// 将IImage转换为Bitmap
        /// </summary>
        public static System.Drawing.Bitmap? ToBitmap(this IImage image)
        {
            if (image == null) return null;

            try
            {
                // 先转换为Mat，然后转换为Bitmap
                using var mat = image.ImageToMat();
                return OpenCvSharp.Extensions.BitmapConverter.ToBitmap(mat);
            }
            catch (Exception ex)
            {
                throw new Exception($"IImage转换为Bitmap失败: {ex.Message}", ex);
            }
        }
    }
}
